#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import pytest
import warnings

from sqlalchemy import select
from sqlalchemy.orm import aliased

from sqlalchemy_crud_plus.errors import ColumnSortError, ModelColumnError, SelectOperatorError
from sqlalchemy_crud_plus.utils import (
    apply_sorting,
    get_column,
    get_sqlalchemy_filter,
    parse_filters,
)
from tests.model import Ins


class TestGetSQLAlchemyFilter:
    """测试get_sqlalchemy_filter函数"""

    def test_valid_operators(self):
        """测试有效的操作符"""
        # 测试比较操作符
        assert get_sqlalchemy_filter('gt', 5) is not None
        assert get_sqlalchemy_filter('lt', 5) is not None
        assert get_sqlalchemy_filter('eq', 5) is not None
        assert get_sqlalchemy_filter('ne', 5) is not None

        # 测试字符串操作符
        assert get_sqlalchemy_filter('like', 'test%') is not None
        assert get_sqlalchemy_filter('ilike', 'test%') is not None
        assert get_sqlalchemy_filter('startswith', 'test') is not None
        assert get_sqlalchemy_filter('endswith', 'test') is not None
        assert get_sqlalchemy_filter('contains', 'test') is not None

    def test_list_operators(self):
        """测试需要列表/元组的操作符"""
        # 正确的用法
        assert get_sqlalchemy_filter('in', [1, 2, 3]) is not None
        assert get_sqlalchemy_filter('not_in', (1, 2, 3)) is not None
        assert get_sqlalchemy_filter('between', [1, 10]) is not None

        # 错误的用法
        with pytest.raises(SelectOperatorError):
            get_sqlalchemy_filter('in', 'not_a_list')

        with pytest.raises(SelectOperatorError):
            get_sqlalchemy_filter('not_in', 123)

        with pytest.raises(SelectOperatorError):
            get_sqlalchemy_filter('between', 'not_a_list')

    def test_arithmetic_operators(self):
        """测试算术操作符"""
        # 允许算术操作
        assert get_sqlalchemy_filter('add', 5, allow_arithmetic=True) is not None
        assert get_sqlalchemy_filter('sub', 5, allow_arithmetic=True) is not None
        assert get_sqlalchemy_filter('mul', 5, allow_arithmetic=True) is not None
        assert get_sqlalchemy_filter('truediv', 5, allow_arithmetic=True) is not None

        # 不允许算术操作
        with pytest.raises(SelectOperatorError):
            get_sqlalchemy_filter('add', 5, allow_arithmetic=False)

        with pytest.raises(SelectOperatorError):
            get_sqlalchemy_filter('concat', 'test', allow_arithmetic=False)

    def test_unsupported_operator(self):
        """测试不支持的操作符"""
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            result = get_sqlalchemy_filter('unsupported_op', 'value')
            assert result is None
            assert len(w) == 1
            assert issubclass(w[0].category, SyntaxWarning)
            assert 'unsupported_op' in str(w[0].message)

    def test_or_operator(self):
        """测试or操作符的特殊处理"""
        result = get_sqlalchemy_filter('or', {'eq': 1, 'gt': 5})
        assert result is None  # or操作符返回None，由其他函数处理


class TestGetColumn:
    """测试get_column函数"""

    def test_valid_column(self):
        """测试有效的列"""
        column = get_column(Ins, 'id')
        assert column is not None
        assert column.name == 'id'

        column = get_column(Ins, 'name')
        assert column is not None
        assert column.name == 'name'

    def test_invalid_column(self):
        """测试无效的列"""
        with pytest.raises(ModelColumnError):
            get_column(Ins, 'nonexistent_column')

    def test_aliased_model(self):
        """测试别名模型"""
        aliased_ins = aliased(Ins)
        column = get_column(aliased_ins, 'id')
        assert column is not None


class TestParseFilters:
    """测试parse_filters函数"""

    def test_simple_filters(self):
        """测试简单过滤器"""
        filters = parse_filters(Ins, id=1, name='test')
        assert len(filters) == 2

    def test_comparison_filters(self):
        """测试比较过滤器"""
        filters = parse_filters(Ins, id__gt=5, name__like='test%')
        assert len(filters) == 2

    def test_in_filters(self):
        """测试in过滤器"""
        filters = parse_filters(Ins, id__in=[1, 2, 3])
        assert len(filters) == 1

    def test_or_filters(self):
        """测试or过滤器"""
        filters = parse_filters(Ins, id__or={'eq': 1, 'gt': 5})
        assert len(filters) == 1

    def test_arithmetic_filters(self):
        """测试算术过滤器"""
        filters = parse_filters(
            Ins,
            id__add={'value': 1, 'condition': {'eq': 2}}
        )
        assert len(filters) == 1

    def test_complex_or_group(self):
        """测试复杂的OR组"""
        filters = parse_filters(
            Ins,
            __or__=[
                {'id__eq': 1},
                {'name__like': 'test%'},
                {'id__gt': 10}
            ]
        )
        assert len(filters) == 1

    def test_mixed_filters(self):
        """测试混合过滤器"""
        filters = parse_filters(
            Ins,
            id__gt=5,
            name__like='test%',
            del_flag=False,
            created_time__between=['2023-01-01', '2023-12-31']
        )
        assert len(filters) == 4

    def test_empty_filters(self):
        """测试空过滤器"""
        filters = parse_filters(Ins)
        assert len(filters) == 0


class TestApplySorting:
    """测试apply_sorting函数"""

    def test_single_column_sort(self):
        """测试单列排序"""
        stmt = select(Ins)
        sorted_stmt = apply_sorting(Ins, stmt, 'id')
        assert sorted_stmt is not None

    def test_multiple_columns_sort(self):
        """测试多列排序"""
        stmt = select(Ins)
        sorted_stmt = apply_sorting(Ins, stmt, ['id', 'name'], ['asc', 'desc'])
        assert sorted_stmt is not None

    def test_default_sort_order(self):
        """测试默认排序顺序"""
        stmt = select(Ins)
        sorted_stmt = apply_sorting(Ins, stmt, ['id', 'name'])
        assert sorted_stmt is not None

    def test_single_sort_order_multiple_columns(self):
        """测试单个排序顺序应用到多列"""
        stmt = select(Ins)
        sorted_stmt = apply_sorting(Ins, stmt, ['id', 'name'], 'desc')
        assert sorted_stmt is not None

    def test_invalid_sort_column(self):
        """测试无效的排序列"""
        stmt = select(Ins)
        with pytest.raises(ModelColumnError):
            apply_sorting(Ins, stmt, 'nonexistent_column')

    def test_mismatched_columns_and_orders(self):
        """测试列和排序顺序数量不匹配"""
        stmt = select(Ins)
        with pytest.raises(ColumnSortError):
            apply_sorting(Ins, stmt, ['id', 'name'], ['asc'])

    def test_invalid_sort_order(self):
        """测试无效的排序顺序"""
        stmt = select(Ins)
        with pytest.raises(SelectOperatorError):
            apply_sorting(Ins, stmt, 'id', 'invalid_order')

    def test_sort_orders_without_columns(self):
        """测试只有排序顺序没有列的情况"""
        stmt = select(Ins)
        with pytest.raises(ValueError):
            apply_sorting(Ins, stmt, None, ['asc'])

    def test_empty_sort_columns(self):
        """测试空排序列"""
        stmt = select(Ins)
        sorted_stmt = apply_sorting(Ins, stmt, [])
        assert sorted_stmt == stmt  # 应该返回原始语句

    def test_none_sort_columns(self):
        """测试None排序列"""
        stmt = select(Ins)
        sorted_stmt = apply_sorting(Ins, stmt, None)
        assert sorted_stmt == stmt  # 应该返回原始语句


class TestFilterCreationHelpers:
    """测试过滤器创建辅助函数"""

    def test_create_or_filters(self):
        """测试_create_or_filters函数"""
        from sqlalchemy_crud_plus.utils import _create_or_filters

        column = get_column(Ins, 'id')
        filters = _create_or_filters(column, 'or', {'eq': 1, 'gt': 5})
        assert len(filters) == 2

    def test_create_arithmetic_filters(self):
        """测试_create_arithmetic_filters函数"""
        from sqlalchemy_crud_plus.utils import _create_arithmetic_filters

        column = get_column(Ins, 'id')
        filters = _create_arithmetic_filters(
            column,
            'add',
            {'value': 1, 'condition': {'eq': 2}}
        )
        assert len(filters) == 1

    def test_create_and_filters(self):
        """测试_create_and_filters函数"""
        from sqlalchemy_crud_plus.utils import _create_and_filters

        column = get_column(Ins, 'id')
        filters = _create_and_filters(column, 'eq', 1)
        assert len(filters) == 1

        # 测试between操作符
        filters = _create_and_filters(column, 'between', [1, 10])
        assert len(filters) == 1


class TestEdgeCases:
    """测试边界情况"""

    def test_none_values(self):
        """测试None值的处理"""
        filters = parse_filters(Ins, name=None)
        assert len(filters) == 1

    def test_empty_string_values(self):
        """测试空字符串值的处理"""
        filters = parse_filters(Ins, name='')
        assert len(filters) == 1

    def test_zero_values(self):
        """测试零值的处理"""
        filters = parse_filters(Ins, id=0)
        assert len(filters) == 1

    def test_boolean_values(self):
        """测试布尔值的处理"""
        filters = parse_filters(Ins, del_flag=True)
        assert len(filters) == 1

        filters = parse_filters(Ins, del_flag=False)
        assert len(filters) == 1

    def test_complex_nested_conditions(self):
        """测试复杂嵌套条件"""
        filters = parse_filters(
            Ins,
            __or__=[
                {'id__eq': 1},
                {'name__or': {'like': 'test%', 'eq': 'exact'}},
                {'id__add': {'value': 1, 'condition': {'between': [2, 10]}}}
            ]
        )
        assert len(filters) == 1
