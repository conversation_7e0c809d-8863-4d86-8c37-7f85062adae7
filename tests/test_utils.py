#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utility functions tests for SQLAlchemy CRUD Plus.
"""
import pytest
from sqlalchemy import select
from sqlalchemy.orm import aliased
from sqlalchemy.ext.asyncio import AsyncSession

from sqlalchemy_crud_plus.utils import (
    get_column,
    get_sqlalchemy_filter,
    parse_filters,
    apply_sorting,
    _create_or_filters,
    _create_arithmetic_filters,
    _create_and_filters,
)
from sqlalchemy_crud_plus.errors import (
    ModelColumnError,
    SelectOperatorError,
    ColumnSortError,
)
from tests.model import Ins


class TestUtilityFunctions:
    """Test utility functions used by CRUD operations."""

    def test_get_column_valid(self):
        """Test getting valid columns from model."""
        name_column = get_column(Ins, 'name')
        assert name_column is not None
        assert hasattr(name_column, 'property')
        
        id_column = get_column(Ins, 'id')
        assert id_column is not None
        
        del_flag_column = get_column(Ins, 'del_flag')
        assert del_flag_column is not None

    def test_get_column_invalid(self):
        """Test getting invalid columns from model."""
        with pytest.raises(ModelColumnError):
            get_column(Ins, 'nonexistent_column')
        
        with pytest.raises(ModelColumnError):
            get_column(Ins, '')

    def test_get_column_with_aliased_model(self):
        """Test getting columns from aliased model."""
        aliased_ins = aliased(Ins)
        
        name_column = get_column(aliased_ins, 'name')
        assert name_column is not None
        
        with pytest.raises(ModelColumnError):
            get_column(aliased_ins, 'nonexistent_column')

    def test_get_sqlalchemy_filter_basic_operators(self):
        """Test basic SQLAlchemy filter operators."""
        column = get_column(Ins, 'name')
        
        # Test equality
        filter_expr = get_sqlalchemy_filter(column, 'eq', 'test')
        assert filter_expr is not None
        
        # Test inequality
        filter_expr = get_sqlalchemy_filter(column, 'ne', 'test')
        assert filter_expr is not None
        
        # Test greater than
        id_column = get_column(Ins, 'id')
        filter_expr = get_sqlalchemy_filter(id_column, 'gt', 5)
        assert filter_expr is not None

    def test_get_sqlalchemy_filter_string_operators(self):
        """Test string-specific operators."""
        column = get_column(Ins, 'name')
        
        # Test LIKE
        filter_expr = get_sqlalchemy_filter(column, 'like', '%test%')
        assert filter_expr is not None
        
        # Test ILIKE
        filter_expr = get_sqlalchemy_filter(column, 'ilike', '%TEST%')
        assert filter_expr is not None
        
        # Test startswith
        filter_expr = get_sqlalchemy_filter(column, 'startswith', 'test')
        assert filter_expr is not None
        
        # Test endswith
        filter_expr = get_sqlalchemy_filter(column, 'endswith', 'test')
        assert filter_expr is not None
        
        # Test contains
        filter_expr = get_sqlalchemy_filter(column, 'contains', 'test')
        assert filter_expr is not None

    def test_get_sqlalchemy_filter_list_operators(self):
        """Test operators that work with lists."""
        column = get_column(Ins, 'id')
        
        # Test IN
        filter_expr = get_sqlalchemy_filter(column, 'in', [1, 2, 3])
        assert filter_expr is not None
        
        # Test NOT IN
        filter_expr = get_sqlalchemy_filter(column, 'not_in', [1, 2, 3])
        assert filter_expr is not None
        
        # Test BETWEEN
        filter_expr = get_sqlalchemy_filter(column, 'between', [1, 10])
        assert filter_expr is not None

    def test_get_sqlalchemy_filter_arithmetic_operators(self):
        """Test arithmetic operators."""
        column = get_column(Ins, 'id')
        
        # Test addition
        filter_expr = get_sqlalchemy_filter(
            column, 'add', 
            {'value': 5, 'condition': {'gt': 10}}
        )
        assert filter_expr is not None
        
        # Test multiplication
        filter_expr = get_sqlalchemy_filter(
            column, 'mul', 
            {'value': 2, 'condition': {'lt': 20}}
        )
        assert filter_expr is not None
        
        # Test division
        filter_expr = get_sqlalchemy_filter(
            column, 'truediv', 
            {'value': 2, 'condition': {'eq': 5}}
        )
        assert filter_expr is not None

    def test_get_sqlalchemy_filter_or_operator(self):
        """Test OR operator."""
        column = get_column(Ins, 'name')
        
        or_conditions = {
            'eq': 'test1',
            'like': '%test2%',
            'startswith': 'test3'
        }
        
        filter_expr = get_sqlalchemy_filter(column, 'or', or_conditions)
        assert filter_expr is not None

    def test_get_sqlalchemy_filter_unsupported_operator(self):
        """Test unsupported operators."""
        column = get_column(Ins, 'name')
        
        # Should return None for unsupported operators
        filter_expr = get_sqlalchemy_filter(column, 'unsupported_op', 'value')
        assert filter_expr is None

    def test_parse_filters_simple(self):
        """Test parsing simple filters."""
        filters = parse_filters(Ins, name='test', id=1)
        assert len(filters) == 2
        
        filters = parse_filters(Ins, name__eq='test')
        assert len(filters) == 1
        
        filters = parse_filters(Ins, id__gt=5)
        assert len(filters) == 1

    def test_parse_filters_complex(self):
        """Test parsing complex filters."""
        filters = parse_filters(
            Ins,
            name__like='%test%',
            id__between=[1, 10],
            del_flag=True
        )
        assert len(filters) == 3

    def test_parse_filters_or_conditions(self):
        """Test parsing OR conditions."""
        # Test simple OR
        filters = parse_filters(
            Ins,
            name__or={'eq': 'test1', 'like': '%test2%'}
        )
        assert len(filters) == 1
        
        # Test complex OR group
        filters = parse_filters(
            Ins,
            __or__=[
                {'name__eq': 'test1'},
                {'id__gt': 5}
            ]
        )
        assert len(filters) == 1

    def test_parse_filters_arithmetic(self):
        """Test parsing arithmetic filters."""
        filters = parse_filters(
            Ins,
            id__add={'value': 5, 'condition': {'gt': 10}}
        )
        assert len(filters) == 1
        
        filters = parse_filters(
            Ins,
            id__mul={'value': 2, 'condition': {'lt': 20}}
        )
        assert len(filters) == 1

    def test_parse_filters_empty(self):
        """Test parsing empty filters."""
        filters = parse_filters(Ins)
        assert len(filters) == 0
        
        filters = parse_filters(Ins, **{})
        assert len(filters) == 0

    def test_apply_sorting_single_column(self):
        """Test applying sorting to single column."""
        stmt = select(Ins)
        
        # Test ascending sort
        sorted_stmt = apply_sorting(Ins, stmt, 'name', 'asc')
        assert sorted_stmt is not None
        
        # Test descending sort
        sorted_stmt = apply_sorting(Ins, stmt, 'name', 'desc')
        assert sorted_stmt is not None
        
        # Test default sort (should be ascending)
        sorted_stmt = apply_sorting(Ins, stmt, 'name')
        assert sorted_stmt is not None

    def test_apply_sorting_multiple_columns(self):
        """Test applying sorting to multiple columns."""
        stmt = select(Ins)
        
        # Test multiple columns with multiple orders
        sorted_stmt = apply_sorting(
            Ins, stmt, 
            ['name', 'id'], 
            ['asc', 'desc']
        )
        assert sorted_stmt is not None
        
        # Test multiple columns with single order
        sorted_stmt = apply_sorting(
            Ins, stmt, 
            ['name', 'id'], 
            'asc'
        )
        assert sorted_stmt is not None

    def test_apply_sorting_validation_errors(self):
        """Test sorting validation errors."""
        stmt = select(Ins)
        
        # Test mismatched columns and orders
        with pytest.raises(ColumnSortError):
            apply_sorting(
                Ins, stmt,
                ['name', 'id'],
                ['asc']  # Only one order for two columns
            )
        
        # Test invalid sort order
        with pytest.raises(SelectOperatorError):
            apply_sorting(Ins, stmt, 'name', 'invalid_order')
        
        # Test invalid column
        with pytest.raises(ModelColumnError):
            apply_sorting(Ins, stmt, 'nonexistent_column')
        
        # Test providing orders without columns
        with pytest.raises(ValueError):
            apply_sorting(Ins, stmt, None, ['asc'])

    def test_apply_sorting_edge_cases(self):
        """Test sorting edge cases."""
        stmt = select(Ins)
        
        # Test with None columns (should return original statement)
        result_stmt = apply_sorting(Ins, stmt, None)
        assert result_stmt is stmt
        
        # Test with empty string columns
        result_stmt = apply_sorting(Ins, stmt, '')
        assert result_stmt is stmt
        
        # Test with empty list
        result_stmt = apply_sorting(Ins, stmt, [])
        assert result_stmt is stmt

    def test_create_or_filters(self):
        """Test OR filter creation helper."""
        column = get_column(Ins, 'name')
        
        or_conditions = {
            'eq': 'test1',
            'like': '%test2%'
        }
        
        or_filters = _create_or_filters(column, 'or', or_conditions)
        assert len(or_filters) == 2

    def test_create_arithmetic_filters(self):
        """Test arithmetic filter creation helper."""
        column = get_column(Ins, 'id')
        
        # Test valid arithmetic condition
        arith_filter = _create_arithmetic_filters(
            column, 'add',
            {'value': 5, 'condition': {'gt': 10}}
        )
        assert arith_filter is not None
        
        # Test invalid arithmetic condition
        arith_filter = _create_arithmetic_filters(
            column, 'add',
            {'invalid': 'structure'}
        )
        assert arith_filter is None

    def test_create_and_filters(self):
        """Test AND filter creation helper."""
        and_conditions = [
            {'name__eq': 'test'},
            {'id__gt': 5}
        ]
        
        and_filters = _create_and_filters(Ins, and_conditions)
        assert len(and_filters) == 2

    def test_filter_edge_cases(self):
        """Test filter edge cases and error handling."""
        column = get_column(Ins, 'name')
        
        # Test with None value
        filter_expr = get_sqlalchemy_filter(column, 'eq', None)
        assert filter_expr is not None
        
        # Test with empty string
        filter_expr = get_sqlalchemy_filter(column, 'eq', '')
        assert filter_expr is not None
        
        # Test with zero value
        id_column = get_column(Ins, 'id')
        filter_expr = get_sqlalchemy_filter(id_column, 'eq', 0)
        assert filter_expr is not None
        
        # Test with boolean value
        bool_column = get_column(Ins, 'del_flag')
        filter_expr = get_sqlalchemy_filter(bool_column, 'eq', True)
        assert filter_expr is not None

    def test_complex_nested_conditions(self):
        """Test complex nested filter conditions."""
        # Test nested OR with AND conditions
        filters = parse_filters(
            Ins,
            __or__=[
                {'name__eq': 'test1', 'id__gt': 5},
                {'name__like': '%test2%', 'del_flag': True}
            ]
        )
        assert len(filters) == 1
        
        # Test mixed conditions
        filters = parse_filters(
            Ins,
            name__startswith='test',
            id__between=[1, 100],
            __or__=[
                {'del_flag': True},
                {'name__endswith': '_special'}
            ]
        )
        assert len(filters) == 3
