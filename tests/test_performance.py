#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能和并发测试
"""
import asyncio
import time
from typing import List

import pytest

from sqlalchemy_crud_plus import CRUDPlus
from tests.model import Ins
from tests.schema import ModelTest


@pytest.mark.asyncio
async def test_bulk_create_performance(async_db_session):
    """测试批量创建的性能"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        
        # 创建大量数据
        data_list = [ModelTest(name=f'bulk_test_{i}') for i in range(1000)]
        
        start_time = time.time()
        results = await crud.create_models(session, data_list)
        end_time = time.time()
        
        assert len(results) == 1000
        execution_time = end_time - start_time
        print(f"批量创建1000条记录耗时: {execution_time:.3f}秒")
        
        # 验证数据正确性
        count = await crud.count(session, name__startswith='bulk_test_')
        assert count == 1000


@pytest.mark.asyncio
async def test_bulk_query_performance(async_db_session):
    """测试批量查询的性能"""
    # 先创建测试数据
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        data_list = [ModelTest(name=f'query_test_{i}') for i in range(500)]
        await crud.create_models(session, data_list)
    
    # 测试查询性能
    async with async_db_session() as session:
        crud = CRUDPlus(Ins)
        
        start_time = time.time()
        results = await crud.select_models(session, name__startswith='query_test_')
        end_time = time.time()
        
        assert len(results) == 500
        execution_time = end_time - start_time
        print(f"查询500条记录耗时: {execution_time:.3f}秒")


@pytest.mark.asyncio
async def test_concurrent_operations(async_db_session):
    """测试并发操作"""
    async def create_records(session_maker, start_idx: int, count: int) -> List[Ins]:
        async with session_maker.begin() as session:
            crud = CRUDPlus(Ins)
            data_list = [ModelTest(name=f'concurrent_{start_idx}_{i}') for i in range(count)]
            return await crud.create_models(session, data_list)
    
    # 并发创建记录
    tasks = []
    for i in range(5):  # 5个并发任务
        task = create_records(async_db_session, i * 100, 50)
        tasks.append(task)
    
    start_time = time.time()
    results = await asyncio.gather(*tasks)
    end_time = time.time()
    
    # 验证结果
    total_created = sum(len(result) for result in results)
    assert total_created == 250  # 5 * 50
    
    execution_time = end_time - start_time
    print(f"并发创建250条记录耗时: {execution_time:.3f}秒")
    
    # 验证数据完整性
    async with async_db_session() as session:
        crud = CRUDPlus(Ins)
        count = await crud.count(session, name__startswith='concurrent_')
        assert count == 250


@pytest.mark.asyncio
async def test_complex_filter_performance(async_db_session):
    """测试复杂过滤器的性能"""
    # 先创建测试数据
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        data_list = []
        for i in range(200):
            data_list.append(ModelTest(name=f'filter_test_{i % 10}'))
        await crud.create_models(session, data_list)
    
    # 测试复杂过滤器性能
    async with async_db_session() as session:
        crud = CRUDPlus(Ins)
        
        start_time = time.time()
        results = await crud.select_models(
            session,
            name__in=['filter_test_1', 'filter_test_2', 'filter_test_3'],
            id__gt=10,
            id__lt=100
        )
        end_time = time.time()
        
        assert len(results) > 0
        execution_time = end_time - start_time
        print(f"复杂过滤器查询耗时: {execution_time:.3f}秒")


@pytest.mark.asyncio
async def test_sorting_performance(async_db_session):
    """测试排序性能"""
    # 先创建测试数据
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        data_list = [ModelTest(name=f'sort_test_{i:04d}') for i in range(300)]
        await crud.create_models(session, data_list)
    
    # 测试排序性能
    async with async_db_session() as session:
        crud = CRUDPlus(Ins)
        
        start_time = time.time()
        results = await crud.select_models_order(
            session,
            sort_columns=['name', 'id'],
            sort_orders=['desc', 'asc'],
            name__startswith='sort_test_'
        )
        end_time = time.time()
        
        assert len(results) == 300
        # 验证排序正确性
        assert results[0].name > results[-1].name  # desc排序
        
        execution_time = end_time - start_time
        print(f"排序300条记录耗时: {execution_time:.3f}秒")


@pytest.mark.asyncio
async def test_update_performance(async_db_session):
    """测试更新性能"""
    # 先创建测试数据
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        data_list = [ModelTest(name=f'update_test_{i}') for i in range(100)]
        created_records = await crud.create_models(session, data_list)
    
    # 测试批量更新性能
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        
        start_time = time.time()
        
        # 批量更新
        update_tasks = []
        for record in created_records:
            task = crud.update_model(
                session, 
                record.id, 
                {'name': f'updated_{record.id}'}
            )
            update_tasks.append(task)
        
        results = await asyncio.gather(*update_tasks)
        end_time = time.time()
        
        assert all(result == 1 for result in results)
        execution_time = end_time - start_time
        print(f"更新100条记录耗时: {execution_time:.3f}秒")


@pytest.mark.asyncio
async def test_delete_performance(async_db_session):
    """测试删除性能"""
    # 先创建测试数据
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        data_list = [ModelTest(name=f'delete_test_{i}') for i in range(100)]
        await crud.create_models(session, data_list)
    
    # 测试批量删除性能
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        
        start_time = time.time()
        result = await crud.delete_model_by_column(
            session,
            allow_multiple=True,
            name__startswith='delete_test_'
        )
        end_time = time.time()
        
        assert result == 100
        execution_time = end_time - start_time
        print(f"删除100条记录耗时: {execution_time:.3f}秒")


@pytest.mark.asyncio
async def test_memory_usage_large_dataset(async_db_session):
    """测试大数据集的内存使用"""
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        
        # 创建大量数据
        batch_size = 1000
        total_records = 5000
        
        for batch_start in range(0, total_records, batch_size):
            data_list = [
                ModelTest(name=f'memory_test_{i}') 
                for i in range(batch_start, min(batch_start + batch_size, total_records))
            ]
            await crud.create_models(session, data_list)
    
    # 查询大量数据
    async with async_db_session() as session:
        crud = CRUDPlus(Ins)
        results = await crud.select_models(session, name__startswith='memory_test_')
        assert len(results) == total_records
    
    final_memory = process.memory_info().rss / 1024 / 1024  # MB
    memory_increase = final_memory - initial_memory
    
    print(f"处理{total_records}条记录，内存增加: {memory_increase:.2f}MB")
    
    # 内存增加应该在合理范围内（这里设置为100MB的阈值）
    assert memory_increase < 100, f"内存使用过多: {memory_increase:.2f}MB"


@pytest.mark.asyncio
async def test_connection_reuse(async_db_session):
    """测试连接复用的效率"""
    crud = CRUDPlus(Ins)
    
    # 使用同一个session进行多次操作
    async with async_db_session.begin() as session:
        start_time = time.time()
        
        for i in range(50):
            data = ModelTest(name=f'reuse_test_{i}')
            await crud.create_model(session, data)
            
            # 立即查询刚创建的记录
            result = await crud.select_model_by_column(session, name=f'reuse_test_{i}')
            assert result is not None
        
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"连接复用50次操作耗时: {execution_time:.3f}秒")


@pytest.mark.asyncio
async def test_transaction_rollback_performance(async_db_session):
    """测试事务回滚的性能影响"""
    crud = CRUDPlus(Ins)
    
    # 测试正常提交
    start_time = time.time()
    async with async_db_session.begin() as session:
        data_list = [ModelTest(name=f'commit_test_{i}') for i in range(100)]
        await crud.create_models(session, data_list)
    commit_time = time.time() - start_time
    
    # 测试回滚
    start_time = time.time()
    try:
        async with async_db_session.begin() as session:
            data_list = [ModelTest(name=f'rollback_test_{i}') for i in range(100)]
            await crud.create_models(session, data_list)
            # 强制抛出异常触发回滚
            raise Exception("Force rollback")
    except Exception:
        pass
    rollback_time = time.time() - start_time
    
    print(f"提交100条记录耗时: {commit_time:.3f}秒")
    print(f"回滚100条记录耗时: {rollback_time:.3f}秒")
    
    # 验证回滚确实生效
    async with async_db_session() as session:
        count = await crud.count(session, name__startswith='rollback_test_')
        assert count == 0
