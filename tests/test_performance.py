#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Performance and optimization tests for SQLAlchemy CRUD Plus.
"""
import asyncio
import time
from typing import List

import pytest

from sqlalchemy_crud_plus import CRUDPlus
from tests.model import Ins
from tests.schema import ModelTest


class TestPerformanceOptimizations:
    """Test performance optimizations and benchmarks."""

    @pytest.mark.asyncio
    async def test_bulk_create_performance(self, async_db_session):
        """Test bulk creation performance with large datasets."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Test with different batch sizes
            batch_sizes = [100, 500, 1000]
            
            for batch_size in batch_sizes:
                data_list = [ModelTest(name=f'perf_test_{i}') for i in range(batch_size)]
                
                start_time = time.time()
                results = await crud.create_models(session, data_list)
                end_time = time.time()
                
                assert len(results) == batch_size
                execution_time = end_time - start_time
                
                # Performance assertion: should complete within reasonable time
                # Adjust threshold based on your requirements
                assert execution_time < 5.0, f"Batch size {batch_size} took {execution_time:.3f}s"
                
                print(f"Created {batch_size} records in {execution_time:.3f}s "
                      f"({batch_size/execution_time:.1f} records/sec)")

    @pytest.mark.asyncio
    async def test_query_performance_with_filters(self, async_db_session):
        """Test query performance with complex filters."""
        # Setup test data
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            data_list = [
                ModelTest(name=f'query_perf_{i % 10}') 
                for i in range(1000)
            ]
            await crud.create_models(session, data_list)
        
        # Test different query patterns
        async with async_db_session() as session:
            crud = CRUDPlus(Ins)
            
            # Simple equality filter
            start_time = time.time()
            results = await crud.select_models(session, name='query_perf_1')
            simple_time = time.time() - start_time
            assert len(results) > 0
            
            # Complex filter with multiple conditions
            start_time = time.time()
            results = await crud.select_models(
                session,
                name__startswith='query_perf_',
                id__gt=10,
                id__lt=500
            )
            complex_time = time.time() - start_time
            assert len(results) > 0
            
            # IN filter
            start_time = time.time()
            results = await crud.select_models(
                session,
                name__in=['query_perf_1', 'query_perf_2', 'query_perf_3']
            )
            in_time = time.time() - start_time
            assert len(results) > 0
            
            print(f"Simple query: {simple_time:.3f}s")
            print(f"Complex query: {complex_time:.3f}s")
            print(f"IN query: {in_time:.3f}s")
            
            # Performance assertions
            assert simple_time < 1.0
            assert complex_time < 2.0
            assert in_time < 1.0

    @pytest.mark.asyncio
    async def test_count_vs_exists_performance(self, async_db_session):
        """Test performance difference between count and exists operations."""
        # Setup test data
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            data_list = [ModelTest(name=f'count_test_{i}') for i in range(500)]
            await crud.create_models(session, data_list)
        
        async with async_db_session() as session:
            crud = CRUDPlus(Ins)
            
            # Test count performance
            start_time = time.time()
            count_result = await crud.count(session, name__startswith='count_test_')
            count_time = time.time() - start_time
            
            # Test exists performance
            start_time = time.time()
            exists_result = await crud.exists(session, name__startswith='count_test_')
            exists_time = time.time() - start_time
            
            assert count_result == 500
            assert exists_result is True
            
            print(f"Count operation: {count_time:.3f}s")
            print(f"Exists operation: {exists_time:.3f}s")
            
            # Note: In small datasets or with SQLite, the performance difference
            # between exists and count might not be significant
            # We just ensure both operations complete successfully
            assert count_time >= 0 and exists_time >= 0

    @pytest.mark.asyncio
    async def test_update_performance_optimization(self, async_db_session):
        """Test update performance with and without allow_multiple optimization."""
        # Setup test data
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            data_list = [ModelTest(name=f'update_test_{i}') for i in range(100)]
            await crud.create_models(session, data_list)
        
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Test update with allow_multiple=True (should skip count check)
            start_time = time.time()
            result = await crud.update_model_by_column(
                session,
                {'name': 'updated_bulk'},
                allow_multiple=True,
                name__startswith='update_test_'
            )
            bulk_time = time.time() - start_time
            
            assert result == 100
            print(f"Bulk update (allow_multiple=True): {bulk_time:.3f}s")
            
            # Create a single record for single update test
            single_data = ModelTest(name='single_update_test')
            single_record = await crud.create_model(session, single_data)
            await session.flush()  # Ensure the record is available

            # Test single update (with count check)
            start_time = time.time()
            result = await crud.update_model_by_column(
                session,
                {'name': 'updated_single'},
                allow_multiple=False,
                name='single_update_test'
            )
            single_time = time.time() - start_time

            assert result == 1  # Only one record should be updated
            print(f"Single update (allow_multiple=False): {single_time:.3f}s")

    @pytest.mark.asyncio
    async def test_concurrent_read_operations(self, async_db_session):
        """Test concurrent read operations performance."""
        # Setup test data
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            data_list = [ModelTest(name=f'concurrent_read_{i}') for i in range(200)]
            await crud.create_models(session, data_list)
        
        async def read_operation(session_factory, filter_value: int):
            """Perform a read operation."""
            async with session_factory() as session:
                crud = CRUDPlus(Ins)
                return await crud.select_models(
                    session, 
                    name=f'concurrent_read_{filter_value}'
                )
        
        # Test concurrent reads
        start_time = time.time()
        tasks = [
            read_operation(async_db_session, i) 
            for i in range(0, 50, 5)  # 10 concurrent tasks
        ]
        results = await asyncio.gather(*tasks)
        concurrent_time = time.time() - start_time
        
        # Verify results
        assert len(results) == 10
        for result in results:
            assert len(result) == 1
        
        print(f"10 concurrent reads: {concurrent_time:.3f}s")
        
        # Test sequential reads for comparison
        start_time = time.time()
        for i in range(0, 50, 5):
            await read_operation(async_db_session, i)
        sequential_time = time.time() - start_time
        
        print(f"10 sequential reads: {sequential_time:.3f}s")
        
        # Concurrent should be faster than sequential
        assert concurrent_time < sequential_time

    @pytest.mark.asyncio
    async def test_memory_efficiency_large_resultset(self, async_db_session):
        """Test memory efficiency with large result sets."""
        try:
            import psutil
            import os

            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_monitoring = True
        except ImportError:
            # Skip memory monitoring if psutil is not available
            memory_monitoring = False
            print("psutil not available, skipping memory monitoring")
        
        # Create large dataset
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create in batches to avoid memory issues during creation
            batch_size = 1000
            total_records = 5000
            
            for batch_start in range(0, total_records, batch_size):
                batch_end = min(batch_start + batch_size, total_records)
                data_list = [
                    ModelTest(name=f'memory_test_{i}') 
                    for i in range(batch_start, batch_end)
                ]
                await crud.create_models(session, data_list)
        
        # Query large result set
        async with async_db_session() as session:
            crud = CRUDPlus(Ins)
            
            start_time = time.time()
            results = await crud.select_models(session, name__startswith='memory_test_')
            query_time = time.time() - start_time
            
            assert len(results) == total_records

            # Check memory usage if monitoring is available
            if memory_monitoring:
                final_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_increase = final_memory - initial_memory

                print(f"Queried {total_records} records in {query_time:.3f}s")
                print(f"Memory increase: {memory_increase:.2f}MB")

                # Memory usage should be reasonable (adjust threshold as needed)
                assert memory_increase < 200, f"Memory usage too high: {memory_increase:.2f}MB"
            else:
                print(f"Queried {total_records} records in {query_time:.3f}s")

            assert query_time < 5.0, f"Query too slow: {query_time:.3f}s"

    @pytest.mark.asyncio
    async def test_sorting_performance(self, async_db_session):
        """Test sorting performance with different column types and sizes."""
        # Setup test data with varied content
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            data_list = []
            for i in range(500):
                # Create varied names for sorting
                name = f"sort_test_{i:04d}_{chr(65 + (i % 26))}"
                data_list.append(ModelTest(name=name))
            await crud.create_models(session, data_list)
        
        async with async_db_session() as session:
            crud = CRUDPlus(Ins)
            
            # Test single column sort
            start_time = time.time()
            results = await crud.select_models_order(
                session,
                sort_columns='name',
                sort_orders='asc',
                name__startswith='sort_test_'
            )
            single_sort_time = time.time() - start_time
            
            assert len(results) == 500
            # Verify sorting
            names = [r.name for r in results]
            assert names == sorted(names)
            
            # Test multi-column sort
            start_time = time.time()
            results = await crud.select_models_order(
                session,
                sort_columns=['name', 'id'],
                sort_orders=['desc', 'asc'],
                name__startswith='sort_test_'
            )
            multi_sort_time = time.time() - start_time
            
            assert len(results) == 500
            
            print(f"Single column sort: {single_sort_time:.3f}s")
            print(f"Multi-column sort: {multi_sort_time:.3f}s")
            
            # Performance assertions
            assert single_sort_time < 2.0
            assert multi_sort_time < 3.0

    @pytest.mark.asyncio
    async def test_transaction_performance(self, async_db_session):
        """Test transaction performance and rollback efficiency."""
        crud = CRUDPlus(Ins)
        
        # Test commit performance
        start_time = time.time()
        async with async_db_session.begin() as session:
            data_list = [ModelTest(name=f'commit_test_{i}') for i in range(100)]
            await crud.create_models(session, data_list)
        commit_time = time.time() - start_time
        
        # Test rollback performance
        start_time = time.time()
        try:
            async with async_db_session.begin() as session:
                data_list = [ModelTest(name=f'rollback_test_{i}') for i in range(100)]
                await crud.create_models(session, data_list)
                # Force rollback
                raise Exception("Intentional rollback")
        except Exception:
            pass
        rollback_time = time.time() - start_time
        
        print(f"Commit 100 records: {commit_time:.3f}s")
        print(f"Rollback 100 records: {rollback_time:.3f}s")
        
        # Verify rollback worked
        async with async_db_session() as session:
            count = await crud.count(session, name__startswith='rollback_test_')
            assert count == 0
        
        # Verify commit worked
        async with async_db_session() as session:
            count = await crud.count(session, name__startswith='commit_test_')
            assert count == 100
        
        # Performance assertions
        assert commit_time < 3.0
        assert rollback_time < 2.0
