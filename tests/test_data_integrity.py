#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据完整性和边界情况测试
"""
import pytest
from datetime import datetime, timedelta

from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import DeclarativeBase, relationship

from sqlalchemy_crud_plus import CRUDPlus
from tests.model import Ins, InsPks
from tests.schema import ModelTest, ModelTestPks


class TestBase(DeclarativeBase):
    pass


class User(TestBase):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    posts = relationship("Post", back_populates="author")


class Post(TestBase):
    __tablename__ = 'posts'
    
    id = Column(Integer, primary_key=True)
    title = Column(String(200), nullable=False)
    content = Column(String(1000))
    author_id = Column(Integer, ForeignKey('users.id'))
    published_at = Column(DateTime)
    
    author = relationship("User", back_populates="posts")


@pytest.mark.asyncio
async def test_unicode_data_handling(async_db_session):
    """测试Unicode数据处理"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        
        # 测试各种Unicode字符
        unicode_names = [
            '测试中文',
            'тест русский',
            'テスト日本語',
            '🚀 Emoji test',
            'Ñoño español',
            'café français',
            'Ελληνικά'
        ]
        
        created_records = []
        for name in unicode_names:
            data = ModelTest(name=name)
            record = await crud.create_model(session, data)
            created_records.append(record)
        
        # 验证数据正确保存和查询
        for i, record in enumerate(created_records):
            queried = await crud.select_model(session, record.id)
            assert queried.name == unicode_names[i]


@pytest.mark.asyncio
async def test_large_text_data(async_db_session):
    """测试大文本数据处理"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        
        # 创建大文本（接近字段限制）
        large_text = 'A' * 60  # 接近String(64)的限制
        data = ModelTest(name=large_text)
        record = await crud.create_model(session, data)
        
        # 验证数据完整性
        queried = await crud.select_model(session, record.id)
        assert queried.name == large_text
        assert len(queried.name) == 60


@pytest.mark.asyncio
async def test_special_characters_in_filters(async_db_session):
    """测试过滤器中的特殊字符"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        
        # 创建包含特殊字符的数据
        special_names = [
            "test'quote",
            'test"double',
            'test\\backslash',
            'test%percent',
            'test_underscore',
            'test[bracket]',
            'test(paren)',
            'test{brace}'
        ]
        
        for name in special_names:
            data = ModelTest(name=name)
            await crud.create_model(session, data)
        
        # 测试精确匹配查询
        for name in special_names:
            result = await crud.select_model_by_column(session, name=name)
            assert result is not None
            assert result.name == name
        
        # 测试LIKE查询
        result = await crud.select_model_by_column(session, name__like="test'%")
        assert result is not None
        assert result.name == "test'quote"


@pytest.mark.asyncio
async def test_null_and_empty_values(async_db_session):
    """测试NULL和空值处理"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        
        # 测试空字符串
        data = ModelTest(name='')
        record = await crud.create_model(session, data)
        queried = await crud.select_model(session, record.id)
        assert queried.name == ''
        
        # 测试查询空字符串
        result = await crud.select_model_by_column(session, name='')
        assert result is not None
        assert result.name == ''
        
        # 测试is和is_not操作符
        result = await crud.select_model_by_column(session, name__is_not=None)
        assert result is not None


@pytest.mark.asyncio
async def test_datetime_handling(async_db_session):
    """测试日期时间处理"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        
        # 创建记录
        data = ModelTest(name='datetime_test')
        record = await crud.create_model(session, data)
        
        # 测试日期时间查询
        now = datetime.now()
        yesterday = now - timedelta(days=1)
        tomorrow = now + timedelta(days=1)
        
        # 查询今天创建的记录
        result = await crud.select_model_by_column(
            session, 
            created_time__between=[yesterday, tomorrow]
        )
        assert result is not None
        
        # 查询今天之前创建的记录（应该为空）
        result = await crud.select_model_by_column(
            session,
            created_time__lt=yesterday
        )
        assert result is None


@pytest.mark.asyncio
async def test_boolean_field_operations(async_db_session):
    """测试布尔字段操作"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        
        # 创建不同del_flag值的记录
        data1 = ModelTest(name='active_record')
        record1 = await crud.create_model(session, data1, del_flag=False)
        
        data2 = ModelTest(name='deleted_record')
        record2 = await crud.create_model(session, data2, del_flag=True)
        
        # 测试布尔值查询
        active_records = await crud.select_models(session, del_flag=False)
        deleted_records = await crud.select_models(session, del_flag=True)
        
        assert len(active_records) >= 1
        assert len(deleted_records) >= 1
        
        # 测试is操作符
        result = await crud.select_model_by_column(session, del_flag__is=True)
        assert result is not None
        assert result.del_flag is True


@pytest.mark.asyncio
async def test_composite_primary_key_edge_cases(create_test_model_pks, async_db_session):
    """测试复合主键的边界情况"""
    async with async_db_session() as session:
        crud = CRUDPlus(InsPks)
        
        # 测试查询存在的复合主键
        result = await crud.select_model(session, (1, 'men'))
        assert result is not None
        assert result.id == 1
        assert result.sex == 'men'
        
        # 测试查询不存在的复合主键
        result = await crud.select_model(session, (999, 'unknown'))
        assert result is None
        
        # 测试部分匹配（应该失败）
        with pytest.raises(Exception):  # CompositePrimaryKeysError
            await crud.select_model(session, (1,))


@pytest.mark.asyncio
async def test_transaction_isolation(async_db_session):
    """测试事务隔离"""
    crud = CRUDPlus(Ins)
    
    # 在一个事务中创建记录
    async with async_db_session.begin() as session1:
        data = ModelTest(name='isolation_test')
        record = await crud.create_model(session1, data)
        record_id = record.id
        
        # 在另一个session中查询（应该看不到未提交的数据）
        async with async_db_session() as session2:
            result = await crud.select_model(session2, record_id)
            # 根据隔离级别，可能看不到未提交的数据
            # 这里主要测试不会抛出异常
    
    # 事务提交后应该能查询到
    async with async_db_session() as session:
        result = await crud.select_model(session, record_id)
        assert result is not None
        assert result.name == 'isolation_test'


@pytest.mark.asyncio
async def test_concurrent_updates(async_db_session):
    """测试并发更新"""
    import asyncio
    
    # 先创建一条记录
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        data = ModelTest(name='concurrent_test')
        record = await crud.create_model(session, data)
        record_id = record.id
    
    # 并发更新同一条记录
    async def update_record(name_suffix):
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            update_data = ModelTest(name=f'updated_{name_suffix}')
            return await crud.update_model(session, record_id, update_data)
    
    # 启动多个并发更新
    tasks = [update_record(f'task_{i}') for i in range(3)]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 至少有一个更新成功
    successful_updates = [r for r in results if isinstance(r, int) and r > 0]
    assert len(successful_updates) >= 1
    
    # 验证最终状态
    async with async_db_session() as session:
        crud = CRUDPlus(Ins)
        final_record = await crud.select_model(session, record_id)
        assert final_record is not None
        assert 'updated_' in final_record.name


@pytest.mark.asyncio
async def test_data_consistency_after_rollback(async_db_session):
    """测试回滚后的数据一致性"""
    crud = CRUDPlus(Ins)
    
    # 记录初始状态
    async with async_db_session() as session:
        initial_count = await crud.count(session)
    
    # 在事务中创建数据然后回滚
    try:
        async with async_db_session.begin() as session:
            data_list = [ModelTest(name=f'rollback_test_{i}') for i in range(5)]
            await crud.create_models(session, data_list)
            
            # 验证事务内可以看到数据
            count_in_transaction = await crud.count(session, name__startswith='rollback_test_')
            assert count_in_transaction == 5
            
            # 强制回滚
            raise Exception("Force rollback")
    except Exception:
        pass
    
    # 验证回滚后数据一致性
    async with async_db_session() as session:
        final_count = await crud.count(session)
        rollback_count = await crud.count(session, name__startswith='rollback_test_')
        
        assert final_count == initial_count
        assert rollback_count == 0


@pytest.mark.asyncio
async def test_update_with_same_values(async_db_session):
    """测试使用相同值更新"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        
        # 创建记录
        data = ModelTest(name='same_value_test')
        record = await crud.create_model(session, data)
        
        # 使用相同值更新
        update_data = ModelTest(name='same_value_test')
        result = await crud.update_model(session, record.id, update_data)
        assert result == 1
        
        # 验证数据没有变化
        updated_record = await crud.select_model(session, record.id)
        assert updated_record.name == 'same_value_test'


@pytest.mark.asyncio
async def test_delete_and_recreate(async_db_session):
    """测试删除后重新创建"""
    crud = CRUDPlus(Ins)
    
    # 创建记录
    async with async_db_session.begin() as session:
        data = ModelTest(name='delete_recreate_test')
        record = await crud.create_model(session, data)
        record_id = record.id
    
    # 删除记录
    async with async_db_session.begin() as session:
        result = await crud.delete_model(session, record_id)
        assert result == 1
    
    # 验证删除成功
    async with async_db_session() as session:
        result = await crud.select_model(session, record_id)
        assert result is None
    
    # 重新创建同名记录
    async with async_db_session.begin() as session:
        data = ModelTest(name='delete_recreate_test')
        new_record = await crud.create_model(session, data)
        # 新记录应该有不同的ID
        assert new_record.id != record_id
        assert new_record.name == 'delete_recreate_test'


@pytest.mark.asyncio
async def test_logical_deletion_integrity(async_db_session):
    """测试逻辑删除的完整性"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        
        # 创建记录
        data = ModelTest(name='logical_delete_test')
        record = await crud.create_model(session, data)
        
        # 逻辑删除
        result = await crud.delete_model_by_column(
            session, 
            logical_deletion=True,
            name='logical_delete_test'
        )
        assert result == 1
        
        # 验证记录仍然存在但标记为删除
        deleted_record = await crud.select_model(session, record.id)
        assert deleted_record is not None
        assert deleted_record.del_flag is True
        
        # 验证通过del_flag查询
        active_records = await crud.select_models(session, del_flag=False)
        deleted_records = await crud.select_models(session, del_flag=True)
        
        assert any(r.id == record.id for r in deleted_records)
        assert not any(r.id == record.id for r in active_records)
