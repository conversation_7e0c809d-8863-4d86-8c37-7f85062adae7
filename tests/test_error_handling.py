#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试错误处理和边界情况
"""
import pytest

from sqlalchemy import <PERSON>umn, Integer, String
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import DeclarativeBase

from sqlalchemy_crud_plus import CRUDPlus
from sqlalchemy_crud_plus.errors import (
    CompositePrimaryKeysError,
    ModelColumnError,
    MultipleResultsError,
    SelectOperatorError,
)
from tests.model import Ins, InsPks
from tests.schema import ModelTest





@pytest.mark.asyncio
async def test_composite_primary_key_error(async_db_session):
    """测试复合主键参数数量不匹配的错误"""
    async with async_db_session() as session:
        crud = CRUDPlus(InsPks)
        
        # 测试参数数量不足
        with pytest.raises(CompositePrimaryKeysError):
            await crud.select_model(session, (1,))  # 缺少第二个主键值
        
        # 测试参数数量过多
        with pytest.raises(CompositePrimaryKeysError):
            await crud.select_model(session, (1, 'men', 'extra'))


@pytest.mark.asyncio
async def test_model_column_error():
    """测试模型列不存在的错误"""
    crud = CRUDPlus(Ins)
    
    # 测试不存在的列
    with pytest.raises(ModelColumnError):
        await crud.select(nonexistent_column='value')


@pytest.mark.asyncio
async def test_multiple_results_error_update(create_test_model, async_db_session):
    """测试更新多条记录时的错误"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        data = ModelTest(name='updated_name')
        
        # 不允许多条记录更新时应该抛出错误
        with pytest.raises(MultipleResultsError):
            await crud.update_model_by_column(
                session, data, allow_multiple=False, name__startswith='name'
            )


@pytest.mark.asyncio
async def test_multiple_results_error_delete(create_test_model, async_db_session):
    """测试删除多条记录时的错误"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        
        # 不允许多条记录删除时应该抛出错误
        with pytest.raises(MultipleResultsError):
            await crud.delete_model_by_column(
                session, allow_multiple=False, name__startswith='name'
            )


@pytest.mark.asyncio
async def test_logical_deletion_column_not_exists(async_db_session):
    """测试逻辑删除时列不存在的错误"""
    # 创建一个没有del_flag列的模型的CRUD实例
    from sqlalchemy import Column, Integer, String
    from sqlalchemy.orm import DeclarativeBase

    class TempBase(DeclarativeBase):
        pass

    class TempModel(TempBase):
        __tablename__ = 'temp_model'
        id = Column(Integer, primary_key=True)
        name = Column(String(50))

    crud = CRUDPlus(TempModel)

    async with async_db_session() as session:
        with pytest.raises(ModelColumnError):
            await crud.delete_model_by_column(
                session, logical_deletion=True, deleted_flag_column='del_flag', id=1
            )


@pytest.mark.asyncio
async def test_select_operator_error():
    """测试选择操作符错误"""
    from sqlalchemy_crud_plus.utils import get_sqlalchemy_filter
    
    # 测试in操作符值类型错误
    with pytest.raises(SelectOperatorError):
        get_sqlalchemy_filter('in', 'not_a_list')
    
    # 测试not_in操作符值类型错误
    with pytest.raises(SelectOperatorError):
        get_sqlalchemy_filter('not_in', 'not_a_list')
    
    # 测试between操作符值类型错误
    with pytest.raises(SelectOperatorError):
        get_sqlalchemy_filter('between', 'not_a_list')


@pytest.mark.asyncio
async def test_empty_filters(async_db_session):
    """测试空过滤器的情况"""
    async with async_db_session() as session:
        crud = CRUDPlus(Ins)
        
        # 测试空过滤器的count
        result = await crud.count(session)
        assert result == 0
        
        # 测试空过滤器的exists
        result = await crud.exists(session)
        assert result is False
        
        # 测试空过滤器的select_models
        result = await crud.select_models(session)
        assert len(result) == 0


@pytest.mark.asyncio
async def test_nonexistent_record_operations(async_db_session):
    """测试对不存在记录的操作"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        
        # 测试查询不存在的记录
        result = await crud.select_model(session, 999)
        assert result is None
        
        # 测试更新不存在的记录
        data = ModelTest(name='updated_name')
        result = await crud.update_model(session, 999, data)
        assert result == 0
        
        # 测试删除不存在的记录
        result = await crud.delete_model(session, 999)
        assert result == 0


@pytest.mark.asyncio
async def test_invalid_sort_parameters():
    """测试无效的排序参数"""
    from sqlalchemy_crud_plus.utils import apply_sorting
    from sqlalchemy import select
    
    crud = CRUDPlus(Ins)
    stmt = select(Ins)
    
    # 测试排序列和排序顺序数量不匹配
    from sqlalchemy_crud_plus.errors import ColumnSortError
    with pytest.raises(ColumnSortError):
        apply_sorting(Ins, stmt, ['id', 'name'], ['asc'])
    
    # 测试无效的排序顺序
    with pytest.raises(SelectOperatorError):
        apply_sorting(Ins, stmt, ['id'], ['invalid_order'])


@pytest.mark.asyncio
async def test_create_with_kwargs(async_db_session):
    """测试创建时使用额外的kwargs参数"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        data = ModelTest(name='test_name')
        
        # 使用kwargs添加额外字段
        result = await crud.create_model(session, data, del_flag=True)
        assert result.name == 'test_name'
        assert result.del_flag is True


@pytest.mark.asyncio
async def test_update_with_dict(async_db_session):
    """测试使用字典进行更新"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)

        # 先创建一条记录
        data = ModelTest(name='original_name')
        created = await crud.create_model(session, data)
        await session.flush()  # 确保记录被保存

        # 使用字典更新
        update_data = {'name': 'updated_name'}
        result = await crud.update_model(session, created.id, update_data)
        assert result == 1

        # 验证更新结果
        updated = await crud.select_model(session, created.id)
        assert updated.name == 'updated_name'


@pytest.mark.asyncio
async def test_select_with_additional_whereclause(create_test_model, async_db_session):
    """测试select_model方法使用额外的whereclause参数"""
    async with async_db_session() as session:
        crud = CRUDPlus(Ins)
        
        # 使用额外的whereclause
        result = await crud.select_model(session, 1, Ins.name == 'name_1')
        assert result is not None
        assert result.name == 'name_1'
        
        # 使用不匹配的whereclause
        result = await crud.select_model(session, 1, Ins.name == 'wrong_name')
        assert result is None


@pytest.mark.asyncio
async def test_count_with_zero_result(async_db_session):
    """测试count返回0的情况"""
    async with async_db_session() as session:
        crud = CRUDPlus(Ins)
        
        # 查询不存在的记录
        result = await crud.count(session, id=999)
        assert result == 0


@pytest.mark.asyncio
async def test_flush_and_commit_parameters(async_db_session):
    """测试flush和commit参数的功能"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        data = ModelTest(name='test_flush_commit')
        
        # 测试flush参数
        result = await crud.create_model(session, data, flush=True)
        assert result.id is not None  # flush后应该有ID
        
        # 测试commit参数（在事务中测试可能有限制，但至少确保不报错）
        data2 = ModelTest(name='test_commit')
        result2 = await crud.create_model(session, data2, commit=False)
        assert result2.name == 'test_commit'


@pytest.mark.asyncio
async def test_create_models_with_kwargs(async_db_session):
    """测试批量创建时使用kwargs"""
    async with async_db_session.begin() as session:
        crud = CRUDPlus(Ins)
        data_list = [ModelTest(name=f'test_{i}') for i in range(3)]
        
        # 使用kwargs添加额外字段
        results = await crud.create_models(session, data_list, del_flag=True)
        assert len(results) == 3
        for result in results:
            assert result.del_flag is True


@pytest.mark.asyncio
async def test_arithmetic_operations_edge_cases(create_test_model, async_db_session):
    """测试算术操作的边界情况"""
    async with async_db_session() as session:
        crud = CRUDPlus(Ins)
        
        # 测试除零情况（SQLite会返回NULL）
        result = await crud.select_model_by_column(
            session, id__truediv={'value': 0, 'condition': {'is': None}}
        )
        assert result is not None


@pytest.mark.asyncio
async def test_or_filter_edge_cases(create_test_model, async_db_session):
    """测试OR过滤器的边界情况"""
    async with async_db_session() as session:
        crud = CRUDPlus(Ins)

        # 测试单个OR条件
        result = await crud.select_model_by_column(session, id__or={'eq': 1})
        assert result is not None
        assert result.id == 1

        # 测试多个OR条件
        result = await crud.select_model_by_column(session, id__or={'eq': 1, 'eq': 2})
        assert result is not None
